import os
import argparse
import time
import torch
import pickle
from datetime import datetime

from openprompt.prompts import SoftVerbalizer, ManualTemplate
from openprompt.utils.reproduciblity import set_seed

from util.utils import load_plm_from_config, print_info
from util.data_loader import SinglePathPromptDataLoader
from processor import PROCESSOR

# Models
from models.hierVerb import HierVerbPromptForClassification as TrainEvalModel
from models.embedding_chy import HierVerbPromptForClassification as EmbeddingModel
from models.topk_chy import HierVerbPromptForClassification as TopKModel


def build_dataloaders(args, processor, tokenizer, WrapperClass):
    # speed up tokenizers
    os.environ.setdefault("TOKENIZERS_PARALLELISM", "true")

    max_seq_l = args.max_seq_lens
    batch_s = args.batch_size

    # ensure template file
    if args.multi_mask:
        template_file = f"{args.dataset}_mask_template.txt"
    else:
        template_file = "manual_template.txt"
    template_dir = os.path.join(os.path.dirname(__file__), "template")
    os.makedirs(template_dir, exist_ok=True)
    template_path = os.path.join(template_dir, template_file)
    if not os.path.exists(template_path):
        text_mask = [f'{i + 1} level: {{"mask"}}' for i in range(args.depth)]
        with open(template_path, 'w', encoding='utf-8') as fp:
            fp.write(f'It was {" ".join(text_mask)}. {{"placeholder": "text_a"}}')

    mytemplate = ManualTemplate(tokenizer=tokenizer).from_file(template_path, choice=args.template_id)

    # cache dataloaders to avoid re-tokenizing on reruns
    if args.dataset == "wos":
        full_name = "WebOfScience"
    elif args.dataset == "dbp":
        full_name = "DBpedia"
    else:
        full_name = args.dataset

    cache_dir = os.path.join(os.path.dirname(__file__), "dataset", full_name)
    os.makedirs(cache_dir, exist_ok=True)
    train_cache = os.path.join(cache_dir, f"train_dataloader-multi_mask.pt")
    dev_cache = os.path.join(cache_dir, f"dev_dataloader-multi_mask.pt")

    if os.path.exists(train_cache):
        train_loader = torch.load(train_cache, weights_only=False)
    else:
        train_loader = SinglePathPromptDataLoader(
            dataset=processor.train_example, template=mytemplate, tokenizer=tokenizer,
            tokenizer_wrapper_class=WrapperClass, max_seq_length=max_seq_l,
            decoder_max_length=3, batch_size=batch_s, shuffle=bool(args.shuffle),
            teacher_forcing=False, predict_eos_token=False, truncate_method="tail",
            multi_gpu=True, mode='train')
        torch.save(train_loader, train_cache)

    eval_bs = args.eval_batch_size if hasattr(args, 'eval_batch_size') else min(32, batch_s)
    if os.path.exists(dev_cache):
        dev_loader = torch.load(dev_cache, weights_only=False)
    else:
        dev_loader = SinglePathPromptDataLoader(
            dataset=processor.dev_example, template=mytemplate, tokenizer=tokenizer,
            tokenizer_wrapper_class=WrapperClass, max_seq_length=max_seq_l,
            decoder_max_length=3, batch_size=eval_bs, shuffle=False,
            teacher_forcing=False, predict_eos_token=False, truncate_method="tail",
            multi_gpu=False, mode='dev')
        torch.save(dev_loader, dev_cache)

    return mytemplate, {'train': processor.train_example, 'dev': processor.dev_example}, train_loader, dev_loader


def build_verbalizers(tokenizer, plm, label_list):
    verbalizer_list = []
    for i in range(len(label_list)):
        verbalizer_list.append(SoftVerbalizer(tokenizer, model=plm, classes=label_list[i]))
    return verbalizer_list


def embedding_pkl_path_from_args(args):
    # keep the same naming as embedding_chy.py
    fname = f"_{args.shot}shot_none_{args.seed}_embed_doc_{args.label_description}.pkl"
    return os.path.join(os.path.dirname(os.path.dirname(__file__)), fname)


def do_embedding_eval_and_store(train_loader, dev_loader, args, processor, plm, mytemplate, verbalizers, tokenizer, device):
    # Build embedding model and sync weights from training model by using the same plm
    emb_model = EmbeddingModel(plm=plm, template=mytemplate, verbalizer_list=verbalizers,
                               freeze_plm=args.freeze_plm, args=args, processor=processor,
                               plm_eval_mode=args.plm_eval_mode, use_cuda=torch.cuda.is_available())
    if torch.cuda.is_available():
        emb_model = emb_model.to(device)

    # 1) Generate embeddings for TRAIN set to build store (and it will write a pkl)
    _ = emb_model.evaluate(train_loader, processor, desc="Embed-Train", mode=args.eval_mode, device=device, args=args)

    # 2) Evaluate embedding-style metrics on DEV set (macro/micro)
    emb_scores = emb_model.evaluate(dev_loader, processor, desc="Embed-Dev", mode=args.eval_mode, device=device, args=args)

    # Load the pkl we just wrote to build embedding_store for top-k
    pkl_path = embedding_pkl_path_from_args(args)
    if not os.path.exists(pkl_path):
        raise FileNotFoundError(f"Expected embedding store at {pkl_path}, but not found.")
    emb_list = pickle.load(open(pkl_path, 'rb'))
    embedding_store = {
        'embedding': torch.stack([torch.Tensor(x[1]) if isinstance(x, list) and len(x) > 1 else torch.Tensor(x)
                                  for x in emb_list['embedding']]).to(device),
        'label': emb_list['label']
    }
    return emb_scores, embedding_store


def do_topk_eval(dev_loader, args, processor, plm, mytemplate, verbalizers, embedding_store, device):
    topk_model = TopKModel(plm=plm, template=mytemplate, verbalizer_list=verbalizers,
                           freeze_plm=args.freeze_plm, args=args, processor=processor,
                           plm_eval_mode=args.plm_eval_mode, use_cuda=torch.cuda.is_available())
    if torch.cuda.is_available():
        topk_model = topk_model.to(device)

    scores, _similar = topk_model.evaluate(dev_loader, processor, embedding_store, topk=args.topk,
                                           desc="TopK-Dev", mode=args.eval_mode, device=device, args=args)
    return scores


def main():
    parser = argparse.ArgumentParser("Joint Train + Embedding + TopK Eval with Early Stop")

    # Common args
    parser.add_argument("--model", type=str, default='bert')
    parser.add_argument("--model_name_or_path", default='/home/<USER>/ZhouSQ/DCX/TACL_chinese1/chinese-roberta-wwm-ext')
    parser.add_argument("--dataset", default="wos", type=str)
    parser.add_argument("--shot", type=int, default=30)
    parser.add_argument("--seed", type=int, default=171)
    parser.add_argument("--multi_mask", type=int, default=1)
    parser.add_argument("--dropout", default=0.1, type=float)
    parser.add_argument("--shuffle", default=0, type=int)
    parser.add_argument("--freeze_plm", default=0, type=int)
    parser.add_argument("--plm_eval_mode", default=False)
    parser.add_argument("--template_id", default=0, type=int)
    parser.add_argument("--max_seq_lens", default=512, type=int)
    parser.add_argument("--depth", default=9, type=int)
    parser.add_argument("--mean_verbalizer", default=True, type=bool)
    parser.add_argument("--use_hier_mean", default=1, type=int)
    parser.add_argument("--lm_training", default=1, type=int)
    parser.add_argument("--lm_alpha", default=0.7, type=float)
    parser.add_argument("--lr", default=3e-5, type=float)
    parser.add_argument("--lr2", default=1e-4, type=float)
    parser.add_argument("--max_grad_norm", default=1.0, type=float)
    parser.add_argument("--batch_size", default=64, type=int)
    parser.add_argument("--eval_batch_size", default=32, type=int)
    parser.add_argument("--gradient_accumulation_steps", type=int, default=1)
    parser.add_argument("--max_epochs", type=int, default=30)
    parser.add_argument("--early_stop", type=int, default=2)  # patience for not all-three improving
    parser.add_argument("--eval_every", type=int, default=5)  # patience for not all-three improving
    parser.add_argument("--eval_mode", default=0, type=int)
    parser.add_argument("--label_description", default=0, type=int)
    parser.add_argument("--topk", type=int, default=1)

    # Additional training flags to match model expectations
    parser.add_argument("--multi_label", default=0, type=int)
    parser.add_argument("--multi_verb", default=1, type=int)
    parser.add_argument("--constraint_loss", default=0, type=int)
    parser.add_argument("--cs_mode", default=0, type=int)
    parser.add_argument("--contrastive_loss", default=0, type=int)
    parser.add_argument("--contrastive_alpha", default=0.99, type=float)
    parser.add_argument("--contrastive_level", default=1, type=int)
    parser.add_argument("--use_dropout_sim", default=1, type=int)
    parser.add_argument("--imbalanced_weight", default=True, type=bool)
    parser.add_argument("--imbalanced_weight_reverse", default=True, type=bool)
    parser.add_argument("--use_withoutWrappedLM", default=False, type=bool)

    # Multi-GPU flags (optional)
    parser.add_argument("--use_multi_gpu", default=True, type=bool)
    parser.add_argument("--use_fp16", default=False, type=bool)

    args = parser.parse_args()

    set_seed(args.seed)

    # Device
    if torch.cuda.is_available():
        device = torch.device("cuda")
        print_info(f"Using CUDA with {torch.cuda.device_count()} GPU(s)")
    else:
        device = torch.device("cpu")
        print_info("Using CPU")

    # Processor and label setup
    processor = PROCESSOR[args.dataset](shot=args.shot, seed=args.seed)
    args.depth = len(processor.hier_mapping) + 1
    label_list = processor.label_list

    # PLM + tokenizer
    plm, tokenizer, model_config, WrapperClass = load_plm_from_config(args, args.model_name_or_path)

    # Template and dataloaders
    mytemplate, dataset, train_loader, dev_loader = build_dataloaders(args, processor, tokenizer, WrapperClass)

    # Verbalizers
    verbalizers = build_verbalizers(tokenizer, plm, label_list)

    # Train model
    prompt_model = TrainEvalModel(plm=plm, template=mytemplate, verbalizer_list=verbalizers, tokenizer=tokenizer,
                                  freeze_plm=args.freeze_plm, args=args, processor=processor,
                                  plm_eval_mode=args.plm_eval_mode, use_cuda=torch.cuda.is_available())
    if torch.cuda.is_available():
        prompt_model = prompt_model.to(device)

    # Optimizers
    from transformers.optimization import get_linear_schedule_with_warmup
    from torch.optim import AdamW
    no_decay = ['bias', 'LayerNorm.weight']
    named_parameters = prompt_model.plm.named_parameters()
    optimizer_grouped_parameters1 = [
        {'params': [p for n, p in named_parameters if not any(nd in n for nd in no_decay)], 'weight_decay': 0.01},
        {'params': [p for n, p in named_parameters if any(nd in n for nd in no_decay)], 'weight_decay': 0.0}
    ]
    verbalizer = prompt_model.verbalizer
    optimizer_grouped_parameters2 = [
        {'params': verbalizer.group_parameters_1, "lr": args.lr},
        {'params': verbalizer.group_parameters_2, "lr": args.lr2},
    ]
    optimizer1 = AdamW(optimizer_grouped_parameters1, lr=args.lr)
    optimizer2 = AdamW(optimizer_grouped_parameters2)

    tot_step = len(train_loader) // max(1, args.gradient_accumulation_steps) * args.max_epochs
    warmup_steps = 0
    scheduler1 = get_linear_schedule_with_warmup(optimizer1, num_warmup_steps=warmup_steps, num_training_steps=tot_step)
    scheduler2 = get_linear_schedule_with_warmup(optimizer2, num_warmup_steps=warmup_steps, num_training_steps=tot_step)

    # Tracking best for three evaluation modes
    best = {
        'train_macro': 0.0, 'train_micro': 0.0,
        'emb_macro': 0.0, 'emb_micro': 0.0,
        'topk_macro': 0.0, 'topk_micro': 0.0,
    }
    early_stop_counter = 0

    run_tag = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    ckpt_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'ckpts')
    os.makedirs(ckpt_dir, exist_ok=True)

    for epoch in range(1, args.max_epochs + 1):
        print_info(f"========== Epoch {epoch} ==========")
        prompt_model.train()
        idx = 0
        for batch in train_loader:
            if hasattr(batch, 'cuda'):
                batch = batch.cuda()
            else:
                batch = tuple(t.to(device) if isinstance(t, torch.Tensor) else t for t in batch)
                batch = {"input_ids": batch[0], "attention_mask": batch[1],
                         "label": batch[2], "loss_ids": batch[3]}
            logits, loss, _ = prompt_model(batch)
            loss.backward()
            torch.nn.utils.clip_grad_norm_(prompt_model.parameters(), args.max_grad_norm)
            optimizer1.step(); optimizer2.step()
            scheduler1.step(); scheduler2.step()
            optimizer1.zero_grad(); optimizer2.zero_grad()
            idx += 1

        # Only evaluate every K epochs
        if epoch % max(1, args.eval_every) == 0:
            # 1) Train-style evaluation on DEV
            train_scores = prompt_model.evaluate(dev_loader, processor, desc="Dev", mode=args.eval_mode)
            train_macro = train_scores.get('macro_f1', 0.0)
            train_micro = train_scores.get('micro_f1', 0.0)
            print_info(f"[Dev Eval] macro_f1={train_macro:.4f} micro_f1={train_micro:.4f}")

            # 2) Embedding evaluation on DEV, and also generate TRAIN embedding store
            emb_scores, embedding_store = do_embedding_eval_and_store(
                train_loader, dev_loader, args, processor, plm, mytemplate, verbalizers, tokenizer, device)
            emb_macro = emb_scores.get('macro_f1', 0.0)
            emb_micro = emb_scores.get('micro_f1', 0.0)
            print_info(f"[Embed Eval] macro_f1={emb_macro:.4f} micro_f1={emb_micro:.4f}")

            # 3) TopK evaluation on DEV using freshly generated TRAIN embeddings
            topk_scores = do_topk_eval(dev_loader, args, processor, plm, mytemplate, verbalizers, embedding_store, device)
            topk_macro = topk_scores.get('macro_f1', 0.0)
            topk_micro = topk_scores.get('micro_f1', 0.0)
            print_info(f"[TopK Eval] macro_f1={topk_macro:.4f} micro_f1={topk_micro:.4f}")

            # Decision: save and continue only if all three pairs improved; else early stop
            all_improved = (
                (train_macro > best['train_macro'] and train_micro > best['train_micro']) and
                (emb_macro > best['emb_macro'] and emb_micro > best['emb_micro']) and
                (topk_macro > best['topk_macro'] and topk_micro > best['topk_micro'])
            )
            if all_improved:
                best.update({
                    'train_macro': train_macro, 'train_micro': train_micro,
                    'emb_macro': emb_macro, 'emb_micro': emb_micro,
                    'topk_macro': topk_macro, 'topk_micro': topk_micro,
                })
                ckpt_path = os.path.join(ckpt_dir, f"{run_tag}-epoch{epoch}-macro{train_macro:.4f}-micro{train_micro:.4f}.ckpt")
                torch.save(prompt_model.state_dict(), ckpt_path)
                print_info(f"✅ All improved. Saved checkpoint to {ckpt_path}. Continue training.")
                early_stop_counter = 0
            else:
                early_stop_counter += 1
                print_info(f"⚠️ Not all metrics improved. Early stop counter = {early_stop_counter}/{args.early_stop}")
                if early_stop_counter >= args.early_stop:
                    print_info("⏹ Early stopping.")
                    break
        else:
            print_info(f"⏭ Skip evaluation at epoch {epoch}; eval_every={args.eval_every}")

    print_info("Training finished.")


if __name__ == "__main__":
    main()

